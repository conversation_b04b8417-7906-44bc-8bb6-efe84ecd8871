#include "wifi_provisioning.h"
#include "esp_log.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_netif.h"
#include "nvs_flash.h"
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "freertos/semphr.h"

#include "wifi_provisioning/manager.h"
#include "wifi_provisioning/scheme_ble.h"

static const char *TAG = "wifi_prov";

/* WiFi配网管理器状态 */
static struct {
    bool initialized;
    bool provisioning_started;
    wifi_prov_state_t state;
    wifi_prov_config_t config;
    EventGroupHandle_t event_group;
    SemaphoreHandle_t state_mutex;
} s_prov_mgr = {0};

/* 事件位定义 */
#define PROV_EVENT_STARTED_BIT      BIT0
#define PROV_EVENT_CRED_RECV_BIT    BIT1
#define PROV_EVENT_CRED_FAIL_BIT    BIT2
#define PROV_EVENT_CRED_SUCCESS_BIT BIT3
#define PROV_EVENT_END_BIT          BIT4

/* 内部函数声明 */
static void wifi_prov_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static void ip_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);
static void set_state(wifi_prov_state_t new_state);

esp_err_t wifi_prov_mgr_init(const wifi_prov_config_t *config)
{
    if (config == NULL) {
        ESP_LOGE(TAG, "Invalid config parameter");
        return ESP_ERR_INVALID_ARG;
    }

    if (s_prov_mgr.initialized) {
        ESP_LOGW(TAG, "WiFi provisioning manager already initialized");
        return ESP_OK;
    }

    /* 创建事件组和互斥锁 */
    s_prov_mgr.event_group = xEventGroupCreate();
    if (s_prov_mgr.event_group == NULL) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_ERR_NO_MEM;
    }

    s_prov_mgr.state_mutex = xSemaphoreCreateMutex();
    if (s_prov_mgr.state_mutex == NULL) {
        vEventGroupDelete(s_prov_mgr.event_group);
        ESP_LOGE(TAG, "Failed to create state mutex");
        return ESP_ERR_NO_MEM;
    }

    /* 复制配置 */
    memcpy(&s_prov_mgr.config, config, sizeof(wifi_prov_config_t));
    
    /* 设置默认值 */
    if (strlen(s_prov_mgr.config.device_name) == 0) {
        strcpy(s_prov_mgr.config.device_name, "LinkPet-Device");
    }
    if (strlen(s_prov_mgr.config.pop) == 0) {
        strcpy(s_prov_mgr.config.pop, "abcd1234");
    }
    if (s_prov_mgr.config.timeout_ms == 0) {
        s_prov_mgr.config.timeout_ms = 300000; // 5分钟默认超时
    }

    /* 初始化网络接口 */
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());

    /* 创建默认WiFi STA */
    esp_netif_create_default_wifi_sta();

    /* 初始化WiFi */
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    /* 注册事件处理器 */
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, 
                                               &wifi_prov_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(WIFI_EVENT, ESP_EVENT_ANY_ID, 
                                               &wifi_event_handler, NULL));
    ESP_ERROR_CHECK(esp_event_handler_register(IP_EVENT, IP_EVENT_STA_GOT_IP, 
                                               &ip_event_handler, NULL));

    /* 初始化配网管理器 */
    wifi_prov_mgr_config_t mgr_config = {
        .scheme = wifi_prov_scheme_ble,
        .scheme_event_handler = WIFI_PROV_SCHEME_BLE_EVENT_HANDLER_FREE_BTDM
    };
    ESP_ERROR_CHECK(wifi_prov_mgr_init(mgr_config));

    set_state(WIFI_PROV_STATE_IDLE);
    s_prov_mgr.initialized = true;

    ESP_LOGI(TAG, "WiFi provisioning manager initialized");
    return ESP_OK;
}

esp_err_t wifi_prov_mgr_deinit(void)
{
    if (!s_prov_mgr.initialized) {
        return ESP_OK;
    }

    /* 停止配网服务 */
    if (s_prov_mgr.provisioning_started) {
        wifi_prov_mgr_stop_provisioning();
    }

    /* 反初始化配网管理器 */
    wifi_prov_mgr_deinit();

    /* 注销事件处理器 */
    esp_event_handler_unregister(WIFI_PROV_EVENT, ESP_EVENT_ANY_ID, &wifi_prov_event_handler);
    esp_event_handler_unregister(WIFI_EVENT, ESP_EVENT_ANY_ID, &wifi_event_handler);
    esp_event_handler_unregister(IP_EVENT, IP_EVENT_STA_GOT_IP, &ip_event_handler);

    /* 清理资源 */
    if (s_prov_mgr.event_group) {
        vEventGroupDelete(s_prov_mgr.event_group);
        s_prov_mgr.event_group = NULL;
    }
    if (s_prov_mgr.state_mutex) {
        vSemaphoreDelete(s_prov_mgr.state_mutex);
        s_prov_mgr.state_mutex = NULL;
    }

    memset(&s_prov_mgr, 0, sizeof(s_prov_mgr));
    ESP_LOGI(TAG, "WiFi provisioning manager deinitialized");
    return ESP_OK;
}

esp_err_t wifi_prov_mgr_is_provisioned(bool *provisioned)
{
    if (provisioned == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    if (!s_prov_mgr.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    return wifi_prov_mgr_is_provisioned(provisioned);
}

esp_err_t wifi_prov_mgr_start_provisioning(void)
{
    if (!s_prov_mgr.initialized) {
        ESP_LOGE(TAG, "WiFi provisioning manager not initialized");
        return ESP_ERR_INVALID_STATE;
    }

    if (s_prov_mgr.provisioning_started) {
        ESP_LOGW(TAG, "WiFi provisioning already started");
        return ESP_OK;
    }

    set_state(WIFI_PROV_STATE_STARTING);

    /* 启动配网服务 */
    esp_err_t ret = wifi_prov_mgr_start_provisioning(
        WIFI_PROV_SECURITY_1,
        s_prov_mgr.config.pop,
        s_prov_mgr.config.device_name,
        NULL
    );

    if (ret != ESP_OK) {
        ESP_LOGE(TAG, "Failed to start provisioning: %s", esp_err_to_name(ret));
        set_state(WIFI_PROV_STATE_FAILED);
        return ret;
    }

    s_prov_mgr.provisioning_started = true;
    ESP_LOGI(TAG, "WiFi provisioning started with device name: %s", s_prov_mgr.config.device_name);
    
    return ESP_OK;
}

esp_err_t wifi_prov_mgr_stop_provisioning(void)
{
    if (!s_prov_mgr.initialized || !s_prov_mgr.provisioning_started) {
        return ESP_OK;
    }

    wifi_prov_mgr_stop_provisioning();
    s_prov_mgr.provisioning_started = false;
    set_state(WIFI_PROV_STATE_IDLE);

    ESP_LOGI(TAG, "WiFi provisioning stopped");
    return ESP_OK;
}

esp_err_t wifi_prov_mgr_reset_provisioning(void)
{
    if (!s_prov_mgr.initialized) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = wifi_prov_mgr_reset_provisioning();
    if (ret == ESP_OK) {
        set_state(WIFI_PROV_STATE_IDLE);
        ESP_LOGI(TAG, "WiFi provisioning reset");
    }
    return ret;
}

wifi_prov_state_t wifi_prov_mgr_get_state(void)
{
    wifi_prov_state_t state = WIFI_PROV_STATE_IDLE;
    if (s_prov_mgr.state_mutex && xSemaphoreTake(s_prov_mgr.state_mutex, pdMS_TO_TICKS(100))) {
        state = s_prov_mgr.state;
        xSemaphoreGive(s_prov_mgr.state_mutex);
    }
    return state;
}

esp_err_t wifi_prov_mgr_get_wifi_config(wifi_config_t *wifi_cfg)
{
    if (wifi_cfg == NULL) {
        return ESP_ERR_INVALID_ARG;
    }

    return esp_wifi_get_config(WIFI_IF_STA, wifi_cfg);
}

esp_err_t wifi_prov_mgr_wait(uint32_t timeout_ms)
{
    if (!s_prov_mgr.initialized || !s_prov_mgr.event_group) {
        return ESP_ERR_INVALID_STATE;
    }

    TickType_t timeout_ticks = (timeout_ms == 0) ? portMAX_DELAY : pdMS_TO_TICKS(timeout_ms);
    
    EventBits_t bits = xEventGroupWaitBits(
        s_prov_mgr.event_group,
        PROV_EVENT_CRED_SUCCESS_BIT | PROV_EVENT_CRED_FAIL_BIT | PROV_EVENT_END_BIT,
        pdTRUE,
        pdFALSE,
        timeout_ticks
    );

    if (bits & PROV_EVENT_CRED_SUCCESS_BIT) {
        return ESP_OK;
    } else if (bits & PROV_EVENT_CRED_FAIL_BIT) {
        return ESP_FAIL;
    } else if (bits & PROV_EVENT_END_BIT) {
        return ESP_OK;
    } else {
        return ESP_ERR_TIMEOUT;
    }
}

/* 内部函数实现 */
static void set_state(wifi_prov_state_t new_state)
{
    if (s_prov_mgr.state_mutex && xSemaphoreTake(s_prov_mgr.state_mutex, pdMS_TO_TICKS(100))) {
        wifi_prov_state_t old_state = s_prov_mgr.state;
        s_prov_mgr.state = new_state;
        xSemaphoreGive(s_prov_mgr.state_mutex);

        /* 调用用户回调 */
        if (s_prov_mgr.config.event_cb && old_state != new_state) {
            s_prov_mgr.config.event_cb(new_state, NULL);
        }
    }
}

static void wifi_prov_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    switch (event_id) {
        case WIFI_PROV_START:
            ESP_LOGI(TAG, "Provisioning started");
            set_state(WIFI_PROV_STATE_STARTED);
            if (s_prov_mgr.event_group) {
                xEventGroupSetBits(s_prov_mgr.event_group, PROV_EVENT_STARTED_BIT);
            }
            break;

        case WIFI_PROV_CRED_RECV: {
            wifi_sta_config_t *wifi_sta_cfg = (wifi_sta_config_t *)event_data;
            ESP_LOGI(TAG, "Received Wi-Fi credentials - SSID: %s", (const char *) wifi_sta_cfg->ssid);
            set_state(WIFI_PROV_STATE_CRED_RECV);
            if (s_prov_mgr.event_group) {
                xEventGroupSetBits(s_prov_mgr.event_group, PROV_EVENT_CRED_RECV_BIT);
            }
            break;
        }

        case WIFI_PROV_CRED_FAIL: {
            wifi_prov_sta_fail_reason_t *reason = (wifi_prov_sta_fail_reason_t *)event_data;
            ESP_LOGE(TAG, "Provisioning failed! Reason: %s",
                     (*reason == WIFI_PROV_STA_AUTH_ERROR) ? "Wi-Fi station authentication failed" :
                     "Wi-Fi access-point not found");
            set_state(WIFI_PROV_STATE_FAILED);
            if (s_prov_mgr.event_group) {
                xEventGroupSetBits(s_prov_mgr.event_group, PROV_EVENT_CRED_FAIL_BIT);
            }
            break;
        }

        case WIFI_PROV_CRED_SUCCESS:
            ESP_LOGI(TAG, "Provisioning successful");
            set_state(WIFI_PROV_STATE_SUCCESS);
            if (s_prov_mgr.event_group) {
                xEventGroupSetBits(s_prov_mgr.event_group, PROV_EVENT_CRED_SUCCESS_BIT);
            }
            break;

        case WIFI_PROV_END:
            ESP_LOGI(TAG, "Provisioning ended");
            s_prov_mgr.provisioning_started = false;
            if (s_prov_mgr.event_group) {
                xEventGroupSetBits(s_prov_mgr.event_group, PROV_EVENT_END_BIT);
            }
            /* 如果配置了自动停止，则反初始化管理器 */
            if (s_prov_mgr.config.auto_stop) {
                wifi_prov_mgr_deinit();
            }
            break;

        default:
            break;
    }
}

static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    switch (event_id) {
        case WIFI_EVENT_STA_START:
            ESP_LOGI(TAG, "WiFi station started");
            break;

        case WIFI_EVENT_STA_CONNECTED:
            ESP_LOGI(TAG, "WiFi station connected");
            set_state(WIFI_PROV_STATE_CONNECTING);
            break;

        case WIFI_EVENT_STA_DISCONNECTED: {
            wifi_event_sta_disconnected_t* disconnected = (wifi_event_sta_disconnected_t*) event_data;
            ESP_LOGW(TAG, "WiFi station disconnected, reason: %d", disconnected->reason);

            /* 如果正在配网过程中断开连接，尝试重连 */
            if (s_prov_mgr.state == WIFI_PROV_STATE_CONNECTING ||
                s_prov_mgr.state == WIFI_PROV_STATE_CONNECTED) {
                esp_wifi_connect();
            }
            break;
        }

        default:
            break;
    }
}

static void ip_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP address: " IPSTR, IP2STR(&event->ip_info.ip));
        set_state(WIFI_PROV_STATE_CONNECTED);
    }
}
