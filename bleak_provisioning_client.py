#!/usr/bin/env python3
"""
LinkPet WiFi配网客户端 - 使用Bleak库
通过BLE直接与LinkPet设备通信进行WiFi配网

依赖安装:
pip install bleak

使用方法:
python bleak_provisioning_client.py --ssid "你的WiFi名称" --passphrase "你的WiFi密码"
"""

import asyncio
import argparse
import json
import time
import struct
from bleak import BleakClient, BleakScanner
from bleak.backends.characteristic import BleakGATTCharacteristic

# ESP32 WiFi配网服务的UUID (ESP-IDF标准)
# 这些是ESP-IDF WiFi Provisioning的标准UUID
PROV_SERVICE_UUID = "0000ffff-0000-1000-8000-00805f9b34fb"
PROV_SESSION_UUID = "0000ff51-0000-1000-8000-00805f9b34fb"  # prov-session
PROV_CONFIG_UUID = "0000ff52-0000-1000-8000-00805f9b34fb"   # prov-config
PROV_VERSION_UUID = "0000ff53-0000-1000-8000-00805f9b34fb"  # proto-ver

class LinkPetProvisioning:
    def __init__(self, device_name="LinkPet-Device", pop="abcd1234"):
        self.device_name = device_name
        self.pop = pop
        self.client = None
        self.session_char = None
        self.config_char = None
        self.version_char = None
        
    async def scan_devices(self, timeout=10):
        """扫描LinkPet设备"""
        print(f"扫描LinkPet设备 (超时: {timeout}秒)...")
        
        devices = await BleakScanner.discover(timeout=timeout)
        linkpet_devices = []
        
        for device in devices:
            if device.name and self.device_name.lower() in device.name.lower():
                linkpet_devices.append(device)
                print(f"  发现设备: {device.name} ({device.address})")
        
        return linkpet_devices
    
    async def connect(self, device_address):
        """连接到设备"""
        print(f"连接到设备: {device_address}")
        
        self.client = BleakClient(device_address)
        await self.client.connect()
        
        if not self.client.is_connected:
            raise Exception("设备连接失败")
        
        print("✅ 设备连接成功")
        
        # 获取服务和特征
        services = await self.client.get_services()
        prov_service = None
        
        for service in services:
            if service.uuid.lower() == PROV_SERVICE_UUID.lower():
                prov_service = service
                break
        
        if not prov_service:
            raise Exception("未找到配网服务")
        
        # 获取特征
        for char in prov_service.characteristics:
            if char.uuid.lower() == PROV_SESSION_UUID.lower():
                self.session_char = char
            elif char.uuid.lower() == PROV_CONFIG_UUID.lower():
                self.config_char = char
            elif char.uuid.lower() == PROV_VERSION_UUID.lower():
                self.version_char = char
        
        if not all([self.session_char, self.config_char]):
            raise Exception("配网特征不完整")
        
        print("✅ 配网服务已就绪")
    
    async def get_version(self):
        """获取设备版本信息"""
        if not self.version_char:
            return "未知版本"
        
        try:
            version_data = await self.client.read_gatt_char(self.version_char)
            return version_data.decode('utf-8', errors='ignore')
        except Exception as e:
            print(f"获取版本信息失败: {e}")
            return "未知版本"
    
    async def start_session(self):
        """启动配网会话"""
        print("启动配网会话...")
        
        # 发送会话启动命令
        session_data = {
            "cmd": "start_session",
            "pop": self.pop
        }
        
        data = json.dumps(session_data).encode('utf-8')
        await self.client.write_gatt_char(self.session_char, data)
        
        # 等待响应
        await asyncio.sleep(1)
        response = await self.client.read_gatt_char(self.session_char)
        
        try:
            response_data = json.loads(response.decode('utf-8'))
            if response_data.get("status") == "success":
                print("✅ 配网会话已启动")
                return True
            else:
                print(f"❌ 会话启动失败: {response_data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ 会话响应解析失败: {e}")
            return False
    
    async def scan_wifi(self):
        """扫描WiFi网络"""
        print("扫描WiFi网络...")
        
        scan_cmd = {
            "cmd": "scan_wifi"
        }
        
        data = json.dumps(scan_cmd).encode('utf-8')
        await self.client.write_gatt_char(self.config_char, data)
        
        # 等待扫描完成
        await asyncio.sleep(3)
        response = await self.client.read_gatt_char(self.config_char)
        
        try:
            response_data = json.loads(response.decode('utf-8'))
            if response_data.get("status") == "success":
                wifi_list = response_data.get("wifi_list", [])
                print(f"  发现 {len(wifi_list)} 个WiFi网络")
                for wifi in wifi_list:
                    print(f"    {wifi.get('ssid', 'Unknown')} (信号: {wifi.get('rssi', 'Unknown')} dBm)")
                return wifi_list
            else:
                print("WiFi扫描失败")
                return []
        except Exception as e:
            print(f"WiFi扫描响应解析失败: {e}")
            return []
    
    async def configure_wifi(self, ssid, password):
        """配置WiFi"""
        print(f"配置WiFi: {ssid}")
        
        wifi_config = {
            "cmd": "set_wifi_config",
            "ssid": ssid,
            "password": password
        }
        
        data = json.dumps(wifi_config).encode('utf-8')
        await self.client.write_gatt_char(self.config_char, data)
        
        # 等待配置响应
        await asyncio.sleep(2)
        response = await self.client.read_gatt_char(self.config_char)
        
        try:
            response_data = json.loads(response.decode('utf-8'))
            if response_data.get("status") == "success":
                print("✅ WiFi配置已发送")
                return True
            else:
                print(f"❌ WiFi配置失败: {response_data.get('message', '未知错误')}")
                return False
        except Exception as e:
            print(f"❌ WiFi配置响应解析失败: {e}")
            return False
    
    async def wait_for_connection(self, timeout=60):
        """等待WiFi连接完成"""
        print(f"等待WiFi连接 (超时: {timeout}秒)...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            # 查询连接状态
            status_cmd = {
                "cmd": "get_wifi_status"
            }
            
            data = json.dumps(status_cmd).encode('utf-8')
            await self.client.write_gatt_char(self.config_char, data)
            
            await asyncio.sleep(2)
            response = await self.client.read_gatt_char(self.config_char)
            
            try:
                response_data = json.loads(response.decode('utf-8'))
                status = response_data.get("wifi_status", "unknown")
                
                print(f"  WiFi状态: {status}")
                
                if status == "connected":
                    ip_address = response_data.get("ip_address", "未知")
                    print(f"✅ WiFi连接成功! IP地址: {ip_address}")
                    return True
                elif status == "failed":
                    print("❌ WiFi连接失败")
                    return False
                    
            except Exception as e:
                print(f"状态查询失败: {e}")
            
            await asyncio.sleep(3)
        
        print("❌ WiFi连接超时")
        return False
    
    async def disconnect(self):
        """断开连接"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("设备连接已断开")

async def main():
    parser = argparse.ArgumentParser(description="LinkPet WiFi配网客户端 (Bleak版)")
    
    parser.add_argument("--ssid", required=True, help="WiFi网络名称(SSID)")
    parser.add_argument("--passphrase", required=True, help="WiFi密码")
    parser.add_argument("--device-name", default="LinkPet-Device", help="BLE设备名称")
    parser.add_argument("--pop", default="abcd1234", help="所有权证明字符串")
    parser.add_argument("--scan-timeout", type=int, default=10, help="设备扫描超时时间(秒)")
    parser.add_argument("--connect-timeout", type=int, default=60, help="WiFi连接超时时间(秒)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("LinkPet WiFi配网客户端 (Bleak版)")
    print("=" * 60)
    print(f"目标设备: {args.device_name}")
    print(f"WiFi SSID: {args.ssid}")
    print(f"POP密钥: {args.pop}")
    print("=" * 60)
    
    provisioning = LinkPetProvisioning(args.device_name, args.pop)
    
    try:
        # 1. 扫描设备
        devices = await provisioning.scan_devices(args.scan_timeout)
        if not devices:
            print("❌ 未找到LinkPet设备")
            print("请确保:")
            print("  1. 设备已进入配网模式 (长按按键2)")
            print("  2. 蓝牙已开启")
            print("  3. 设备在蓝牙范围内")
            return False
        
        # 使用第一个找到的设备
        target_device = devices[0]
        
        # 2. 连接设备
        await provisioning.connect(target_device.address)
        
        # 3. 获取版本信息
        version = await provisioning.get_version()
        print(f"设备版本: {version}")
        
        # 4. 启动配网会话
        if not await provisioning.start_session():
            return False
        
        # 5. 扫描WiFi (可选)
        await provisioning.scan_wifi()
        
        # 6. 配置WiFi
        if not await provisioning.configure_wifi(args.ssid, args.passphrase):
            return False
        
        # 7. 等待连接完成
        success = await provisioning.wait_for_connection(args.connect_timeout)
        
        if success:
            print("\n🎉 配网完成! LinkPet设备已成功连接到WiFi网络")
        
        return success
        
    except Exception as e:
        print(f"❌ 配网过程出错: {e}")
        return False
        
    finally:
        await provisioning.disconnect()

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断配网过程")
        exit(1)
