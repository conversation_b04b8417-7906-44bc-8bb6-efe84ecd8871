#ifndef WIFI_PROVISIONING_H
#define WIFI_PROVISIONING_H

#include "esp_err.h"
#include "esp_event.h"
#include "esp_wifi.h"

#ifdef __cplusplus
extern "C" {
#endif

/**
 * @brief WiFi配网状态
 */
typedef enum {
    WIFI_PROV_STATE_IDLE,           /*!< 空闲状态 */
    WIFI_PROV_STATE_STARTING,       /*!< 正在启动配网 */
    WIFI_PROV_STATE_STARTED,        /*!< 配网已启动 */
    WIFI_PROV_STATE_CRED_RECV,      /*!< 已接收凭据 */
    WIFI_PROV_STATE_CONNECTING,     /*!< 正在连接WiFi */
    WIFI_PROV_STATE_CONNECTED,      /*!< WiFi已连接 */
    WIFI_PROV_STATE_FAILED,         /*!< 配网失败 */
    WIFI_PROV_STATE_SUCCESS         /*!< 配网成功 */
} wifi_prov_state_t;

/**
 * @brief WiFi配网事件回调函数类型
 */
typedef void (*wifi_prov_event_cb_t)(wifi_prov_state_t state, void *event_data);

/**
 * @brief WiFi配网配置结构体
 */
typedef struct {
    char device_name[32];           /*!< BLE设备名称 */
    char pop[16];                   /*!< 所有权证明字符串 */
    wifi_prov_event_cb_t event_cb;  /*!< 事件回调函数 */
    bool auto_stop;                 /*!< 是否自动停止配网 */
    uint32_t timeout_ms;            /*!< 配网超时时间(毫秒) */
} wifi_prov_config_t;

/**
 * @brief 初始化WiFi配网管理器
 * 
 * @param config 配网配置
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_ERR_INVALID_ARG: 参数无效
 *         - ESP_ERR_INVALID_STATE: 状态无效
 */
esp_err_t wifi_prov_mgr_init(const wifi_prov_config_t *config);

/**
 * @brief 反初始化WiFi配网管理器
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 */
esp_err_t wifi_prov_mgr_deinit(void);

/**
 * @brief 检查设备是否已配网
 * 
 * @param provisioned 输出参数，true表示已配网
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_ERR_INVALID_ARG: 参数无效
 */
esp_err_t wifi_prov_mgr_is_provisioned(bool *provisioned);

/**
 * @brief 启动WiFi配网服务
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_ERR_INVALID_STATE: 状态无效
 */
esp_err_t wifi_prov_mgr_start_provisioning(void);

/**
 * @brief 停止WiFi配网服务
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 */
esp_err_t wifi_prov_mgr_stop_provisioning(void);

/**
 * @brief 重置配网状态
 * 
 * @return esp_err_t 
 *         - ESP_OK: 成功
 */
esp_err_t wifi_prov_mgr_reset_provisioning(void);

/**
 * @brief 获取当前配网状态
 * 
 * @return wifi_prov_state_t 当前状态
 */
wifi_prov_state_t wifi_prov_mgr_get_state(void);

/**
 * @brief 获取WiFi连接信息
 * 
 * @param wifi_cfg 输出参数，WiFi配置信息
 * @return esp_err_t 
 *         - ESP_OK: 成功
 *         - ESP_ERR_INVALID_ARG: 参数无效
 *         - ESP_ERR_NOT_FOUND: 未找到配置
 */
esp_err_t wifi_prov_mgr_get_wifi_config(wifi_config_t *wifi_cfg);

/**
 * @brief 等待配网完成
 * 
 * @param timeout_ms 超时时间(毫秒)，0表示无限等待
 * @return esp_err_t 
 *         - ESP_OK: 配网成功
 *         - ESP_ERR_TIMEOUT: 超时
 *         - ESP_FAIL: 配网失败
 */
esp_err_t wifi_prov_mgr_wait(uint32_t timeout_ms);

#ifdef __cplusplus
}
#endif

#endif /* WIFI_PROVISIONING_H */
