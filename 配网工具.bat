@echo off
chcp 65001 >nul
title LinkPet WiFi配网工具

echo ========================================
echo LinkPet WiFi配网工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ Python已安装

REM 检查esp-idf-provisioning是否安装
python -c "import esp_prov" >nul 2>&1
if errorlevel 1 (
    echo.
    echo ⚠️  正在安装配网依赖...
    pip install esp-idf-provisioning
    if errorlevel 1 (
        echo ❌ 依赖安装失败
        pause
        exit /b 1
    )
    echo ✅ 依赖安装成功
)

echo.
echo 📱 请确保LinkPet设备已进入配网模式
echo    (长按设备上的按键2)
echo.

REM 获取用户输入
set /p ssid="请输入WiFi网络名称: "
if "%ssid%"=="" (
    echo ❌ WiFi网络名称不能为空
    pause
    exit /b 1
)

set /p password="请输入WiFi密码: "
if "%password%"=="" (
    echo ❌ WiFi密码不能为空
    pause
    exit /b 1
)

echo.
echo 🔄 开始配网...
echo    设备名称: LinkPet-Device
echo    WiFi网络: %ssid%
echo    POP密钥: abcd1234
echo.

REM 运行配网命令
python -m esp_prov --transport ble --service_name LinkPet-Device --pop abcd1234 --ssid "%ssid%" --passphrase "%password%"

if errorlevel 1 (
    echo.
    echo ❌ 配网失败，请检查:
    echo    1. 设备是否已进入配网模式
    echo    2. 蓝牙是否已开启
    echo    3. WiFi密码是否正确
    echo    4. 设备是否在蓝牙范围内
) else (
    echo.
    echo ✅ 配网成功！
    echo 🎉 您的LinkPet设备现在已连接到WiFi网络
)

echo.
pause
