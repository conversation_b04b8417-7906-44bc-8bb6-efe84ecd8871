# LinkPet WiFi配网说明

## 概述

LinkPet设备支持通过BLE (蓝牙低功耗) 进行WiFi配网。用户可以使用手机APP或Python脚本来配置设备的WiFi连接。

## 配网流程

### 1. 设备端操作

1. **进入配网模式**: 长按设备上的按键2，设备将进入WiFi配网模式
2. **指示灯状态**: 设备进入配网模式后，会开始BLE广播
3. **设备信息**:
   - BLE设备名称: `LinkPet-Device`
   - POP密钥: `abcd1234`
   - 安全级别: Security 1 (加密)

### 2. 客户端配网方法

#### 方法一: 使用Python脚本 (推荐)

**安装依赖:**
```bash
pip install bleak
```

**使用设备扫描工具:**
```bash
python linkpet_ble_scanner.py
```

**使用配网协议客户端:**
```bash
python esp_provisioning_protocol.py --ssid "你的WiFi名称" --password "你的WiFi密码"
```

**使用Windows批处理工具:**
```bash
双击运行 配网工具.bat
```

**工具对比:**

| 工具 | 功能 | 推荐度 | 说明 |
|------|------|--------|------|
| `linkpet_ble_scanner.py` | 设备扫描和探索 | ⭐⭐⭐⭐⭐ | 用于检测设备和分析BLE服务 |
| `esp_provisioning_protocol.py` | 配网协议实现 | ⭐⭐⭐ | 简化的配网协议实现 |
| `配网工具.bat` | Windows一键工具 | ⭐⭐⭐⭐ | Windows用户友好界面 |
| ESP BLE Provisioning APP | 官方手机APP | ⭐⭐⭐⭐⭐ | 最稳定可靠的配网方式 |

#### 方法二: 使用手机APP (推荐)

**ESP BLE Provisioning APP:**
- Android: 在Google Play搜索 "ESP BLE Provisioning"
- iOS: 在App Store搜索 "ESP BLE Provisioning"

**配网参数:**
- 设备名称: `LinkPet-Device`
- POP密钥: `abcd1234`
- 安全级别: Security 1

#### 方法三: 使用命令行工具 (需要esp-idf环境)

如果您有ESP-IDF开发环境，可以使用官方工具:

```bash
python $IDF_PATH/tools/esp_prov/esp_prov.py \
    --transport ble \
    --service_name LinkPet-Device \
    --pop abcd1234 \
    --ssid "你的WiFi名称" \
    --passphrase "你的WiFi密码"
```

## 配网状态说明

### 设备状态
- **IDLE**: 空闲状态，未开始配网
- **STARTING**: 正在启动配网服务
- **STARTED**: 配网服务已启动，BLE广播中
- **CRED_RECV**: 已接收WiFi凭据
- **CONNECTING**: 正在连接WiFi
- **CONNECTED**: WiFi已连接成功
- **SUCCESS**: 配网完成
- **FAILED**: 配网失败

### 日志输出示例
```
[1/6] 初始化BLE配网客户端...
[2/6] 配置安全连接 (POP: abcd1234)...
[3/6] 建立配网会话...
[4/6] 获取设备信息...
    设备版本: v1.0
[5/6] 扫描WiFi网络...
    发现 5 个WiFi网络
    找到目标网络: MyWiFi (信号强度: -45 dBm)
[6/6] 发送WiFi配置...
    WiFi凭据已发送

等待配网完成 (超时: 120秒)...
    配网状态: connecting
    配网状态: connected
✅ WiFi配网成功!
    已连接到: MyWiFi
    IP地址: *************
```

## 故障排除

### 常见问题

1. **找不到设备**
   - 确保设备已进入配网模式 (长按按键2)
   - 检查蓝牙是否已开启
   - 确保设备在蓝牙范围内 (通常10米内)

2. **连接失败**
   - 检查POP密钥是否正确 (默认: abcd1234)
   - 尝试重启设备并重新进入配网模式

3. **WiFi连接失败**
   - 检查WiFi密码是否正确
   - 确保WiFi网络支持2.4GHz频段
   - 检查WiFi信号强度是否足够

4. **配网超时**
   - 增加超时时间参数
   - 检查网络环境是否稳定
   - 尝试靠近WiFi路由器

### 调试技巧

1. **启用详细日志**:
   ```bash
   python wifi_provisioning_client.py --ssid "MyWiFi" --passphrase "MyPassword" --verbose
   ```

2. **检查设备日志**:
   使用串口监视器查看设备端的日志输出

3. **重置配网状态**:
   如果设备已配网但需要重新配网，长按按键2会自动重置配网状态

## 安全说明

- 配网过程使用Security 1加密，确保WiFi密码传输安全
- POP密钥用于验证设备身份，防止未授权访问
- 配网完成后，BLE服务会自动停止，节省功耗

## 开发者信息

- **配网协议**: ESP-IDF WiFi Provisioning
- **传输方式**: BLE (Bluetooth Low Energy)
- **安全级别**: Security 1 (SRP6a + AES)
- **支持的WiFi**: 2.4GHz, WPA/WPA2/WPA3
