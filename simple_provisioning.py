#!/usr/bin/env python3
"""
简单的LinkPet WiFi配网脚本
使用ESP-IDF官方配网工具进行配网

安装依赖:
pip install esp-idf-provisioning

使用步骤:
1. 确保LinkPet设备已进入配网模式 (长按按键2)
2. 运行此脚本
3. 按提示输入WiFi信息
"""

import subprocess
import sys
import os

def check_dependencies():
    """检查依赖是否已安装"""
    try:
        import esp_prov
        print("✅ esp-idf-provisioning 已安装")
        return True
    except ImportError:
        print("❌ 缺少依赖: esp-idf-provisioning")
        print("请运行: pip install esp-idf-provisioning")
        return False

def run_provisioning():
    """运行配网过程"""
    print("=" * 60)
    print("LinkPet WiFi配网工具 (简化版)")
    print("=" * 60)
    
    # 获取用户输入
    device_name = input("设备名称 [LinkPet-Device]: ").strip()
    if not device_name:
        device_name = "LinkPet-Device"
    
    ssid = input("WiFi网络名称 (SSID): ").strip()
    if not ssid:
        print("❌ 必须提供WiFi网络名称")
        return False
    
    password = input("WiFi密码: ").strip()
    if not password:
        print("❌ 必须提供WiFi密码")
        return False
    
    pop = input("POP密钥 [abcd1234]: ").strip()
    if not pop:
        pop = "abcd1234"
    
    print("\n配网参数:")
    print(f"  设备名称: {device_name}")
    print(f"  WiFi SSID: {ssid}")
    print(f"  POP密钥: {pop}")
    print()
    
    # 构建命令
    cmd = [
        "python", "-m", "esp_prov",
        "--transport", "ble",
        "--service_name", device_name,
        "--pop", pop,
        "--ssid", ssid,
        "--passphrase", password
    ]
    
    print("开始配网...")
    print("命令:", " ".join(cmd))
    print()
    
    try:
        # 运行配网命令
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ 配网成功!")
            print("输出:", result.stdout)
        else:
            print("❌ 配网失败!")
            print("错误:", result.stderr)
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ 配网超时!")
        return False
    except Exception as e:
        print(f"❌ 配网过程出错: {e}")
        return False
    
    return True

def main():
    print("LinkPet WiFi配网工具")
    print("请确保设备已进入配网模式 (长按按键2)")
    print()
    
    # 检查依赖
    if not check_dependencies():
        return False
    
    # 运行配网
    return run_provisioning()

if __name__ == "__main__":
    success = main()
    
    if success:
        print("\n🎉 配网完成!")
        print("您的LinkPet设备现在应该已连接到WiFi网络")
    else:
        print("\n💡 配网失败，请检查:")
        print("1. 设备是否已进入配网模式")
        print("2. 蓝牙是否已开启")
        print("3. WiFi密码是否正确")
        print("4. 设备是否在蓝牙范围内")
    
    input("\n按回车键退出...")
    sys.exit(0 if success else 1)
