#!/usr/bin/env python3
"""
LinkPet WiFi配网客户端
基于实际扫描到的BLE服务UUID进行配网

依赖安装:
pip install bleak

使用方法:
python linkpet_wifi_provisioning.py --ssid "WiFi名称" --password "WiFi密码"
"""

import asyncio
import argparse
import json
import struct
import time
from bleak import BleakClient, BleakScanner

# LinkPet设备的实际BLE服务UUID (从扫描结果获得)
LINKPET_SERVICE_UUID = "1775244d-6b43-439b-877c-060f2d9bed07"

# LinkPet设备的特征UUID (从扫描结果获得)
LINKPET_CHAR_1 = "1775ff4f-6b43-439b-877c-060f2d9bed07"  # 可能是会话特征
LINKPET_CHAR_2 = "1775ff50-6b43-439b-877c-060f2d9bed07"  # 可能是配置特征
LINKPET_CHAR_3 = "1775ff51-6b43-439b-877c-060f2d9bed07"  # 可能是状态特征
LINKPET_CHAR_4 = "1775ff52-6b43-439b-877c-060f2d9bed07"  # 可能是扫描特征
LINKPET_CHAR_5 = "1775ff53-6b43-439b-877c-060f2d9bed07"  # 可能是版本特征

class LinkPetWiFiProvisioning:
    def __init__(self, device_name="LinkPet-Device", pop="abcd1234"):
        self.device_name = device_name
        self.pop = pop
        self.client = None
        self.characteristics = {}
        
    async def scan_and_connect(self, timeout=15):
        """扫描并连接LinkPet设备"""
        print(f"🔍 扫描LinkPet设备: {self.device_name}")
        
        devices = await BleakScanner.discover(timeout=timeout)
        target_device = None
        
        for device in devices:
            if device.name and self.device_name in device.name:
                target_device = device
                print(f"✅ 找到设备: {device.name} ({device.address})")
                break
        
        if not target_device:
            raise Exception(f"未找到设备: {self.device_name}")
        
        print(f"🔗 连接设备...")
        self.client = BleakClient(target_device.address)
        await self.client.connect()
        
        if not self.client.is_connected:
            raise Exception("设备连接失败")
        
        print("✅ 设备连接成功")
        
        # 查找LinkPet配网服务和特征
        await self._find_characteristics()
        
    async def _find_characteristics(self):
        """查找LinkPet配网特征"""
        print("🔍 查找LinkPet配网服务...")
        
        linkpet_service = None
        for service in self.client.services:
            if service.uuid.lower() == LINKPET_SERVICE_UUID.lower():
                linkpet_service = service
                break
        
        if not linkpet_service:
            raise Exception("未找到LinkPet配网服务")
        
        print(f"✅ 找到LinkPet配网服务: {linkpet_service.uuid}")
        
        # 获取所有特征
        for char in linkpet_service.characteristics:
            char_uuid = char.uuid.lower()
            self.characteristics[char_uuid] = char
            print(f"  📡 特征: {char.uuid} - 属性: {', '.join(char.properties)}")
        
        if len(self.characteristics) < 5:
            print(f"⚠️  只找到 {len(self.characteristics)} 个特征，可能不完整")
    
    async def read_characteristic(self, char_uuid):
        """读取特征数据"""
        char_uuid_lower = char_uuid.lower()
        if char_uuid_lower not in self.characteristics:
            print(f"❌ 特征不存在: {char_uuid}")
            return None
        
        char = self.characteristics[char_uuid_lower]
        if "read" not in char.properties:
            print(f"⚠️  特征不支持读取: {char_uuid}")
            return None
        
        try:
            data = await self.client.read_gatt_char(char)
            print(f"📖 读取 {char_uuid}: {data.hex()} ({len(data)} 字节)")
            return data
        except Exception as e:
            print(f"❌ 读取失败 {char_uuid}: {e}")
            return None
    
    async def write_characteristic(self, char_uuid, data):
        """写入特征数据"""
        char_uuid_lower = char_uuid.lower()
        if char_uuid_lower not in self.characteristics:
            print(f"❌ 特征不存在: {char_uuid}")
            return False
        
        char = self.characteristics[char_uuid_lower]
        if "write" not in char.properties and "write-without-response" not in char.properties:
            print(f"⚠️  特征不支持写入: {char_uuid}")
            return False
        
        try:
            await self.client.write_gatt_char(char, data)
            print(f"📝 写入 {char_uuid}: {data.hex()} ({len(data)} 字节)")
            return True
        except Exception as e:
            print(f"❌ 写入失败 {char_uuid}: {e}")
            return False
    
    async def get_device_info(self):
        """获取设备信息"""
        print("📋 获取设备信息...")
        
        # 尝试从版本特征读取信息
        version_data = await self.read_characteristic(LINKPET_CHAR_5)
        if version_data:
            try:
                # 尝试解析为文本
                version_text = version_data.decode('utf-8', errors='ignore')
                if version_text.isprintable():
                    print(f"  设备版本: {version_text}")
            except:
                pass
        
        # 读取其他特征的初始状态
        for i, char_uuid in enumerate([LINKPET_CHAR_1, LINKPET_CHAR_2, LINKPET_CHAR_3, LINKPET_CHAR_4], 1):
            data = await self.read_characteristic(char_uuid)
            if data and len(data) > 0:
                print(f"  特征{i}初始数据: {data.hex()}")
    
    async def start_provisioning_session(self):
        """启动配网会话"""
        print("🚀 启动配网会话...")
        
        # 尝试不同的会话启动方法
        session_commands = [
            # 方法1: JSON格式
            json.dumps({"cmd": "start_session", "pop": self.pop}).encode('utf-8'),
            # 方法2: 简单字符串
            f"start_session:{self.pop}".encode('utf-8'),
            # 方法3: 二进制格式
            struct.pack('<I', 0x01) + self.pop.encode('utf-8'),
            # 方法4: 只发送POP
            self.pop.encode('utf-8')
        ]
        
        for i, cmd in enumerate(session_commands, 1):
            print(f"  尝试方法{i}: {cmd.hex()}")
            
            # 尝试写入第一个特征 (通常是会话特征)
            if await self.write_characteristic(LINKPET_CHAR_1, cmd):
                await asyncio.sleep(1)
                # 检查响应
                response = await self.read_characteristic(LINKPET_CHAR_1)
                if response and len(response) > 0:
                    print(f"  ✅ 收到响应: {response.hex()}")
                    return True
        
        print("❌ 会话启动失败")
        return False
    
    async def send_wifi_credentials(self, ssid, password):
        """发送WiFi凭据"""
        print(f"📡 发送WiFi凭据: {ssid}")
        
        # 尝试不同的WiFi配置格式
        wifi_configs = [
            # 方法1: JSON格式
            json.dumps({"ssid": ssid, "password": password}).encode('utf-8'),
            # 方法2: 简单格式
            f"wifi:{ssid}:{password}".encode('utf-8'),
            # 方法3: 分段发送 - 先发SSID
            f"ssid:{ssid}".encode('utf-8'),
        ]
        
        for i, config in enumerate(wifi_configs, 1):
            print(f"  尝试配置方法{i}: {config[:50].hex()}...")
            
            # 尝试写入第二个特征 (通常是配置特征)
            if await self.write_characteristic(LINKPET_CHAR_2, config):
                await asyncio.sleep(2)
                
                # 如果是分段发送，再发送密码
                if i == 3:
                    password_cmd = f"password:{password}".encode('utf-8')
                    await self.write_characteristic(LINKPET_CHAR_2, password_cmd)
                    await asyncio.sleep(1)
                
                # 检查响应
                response = await self.read_characteristic(LINKPET_CHAR_2)
                if response:
                    print(f"  📨 配置响应: {response.hex()}")
                    return True
        
        print("❌ WiFi配置发送失败")
        return False
    
    async def check_connection_status(self, timeout=60):
        """检查WiFi连接状态"""
        print(f"⏳ 检查WiFi连接状态 (超时: {timeout}秒)...")
        
        start_time = time.time()
        while (time.time() - start_time) < timeout:
            # 读取状态特征
            status_data = await self.read_characteristic(LINKPET_CHAR_3)
            if status_data:
                try:
                    # 尝试解析状态
                    status_text = status_data.decode('utf-8', errors='ignore')
                    print(f"  状态: {status_text}")
                    
                    if "connected" in status_text.lower():
                        print("✅ WiFi连接成功!")
                        return True
                    elif "failed" in status_text.lower():
                        print("❌ WiFi连接失败")
                        return False
                        
                except:
                    # 如果不是文本，显示十六进制
                    print(f"  状态数据: {status_data.hex()}")
            
            await asyncio.sleep(5)
        
        print("❌ 连接状态检查超时")
        return False
    
    async def disconnect(self):
        """断开连接"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("🔌 设备连接已断开")

async def main():
    parser = argparse.ArgumentParser(description="LinkPet WiFi配网客户端")
    parser.add_argument("--ssid", required=True, help="WiFi网络名称")
    parser.add_argument("--password", required=True, help="WiFi密码")
    parser.add_argument("--device", default="LinkPet-Device", help="设备名称")
    parser.add_argument("--pop", default="abcd1234", help="POP密钥")
    parser.add_argument("--timeout", type=int, default=60, help="连接超时(秒)")
    
    args = parser.parse_args()
    
    print("=" * 60)
    print("LinkPet WiFi配网客户端")
    print("=" * 60)
    print(f"设备名称: {args.device}")
    print(f"WiFi SSID: {args.ssid}")
    print(f"POP密钥: {args.pop}")
    print("=" * 60)
    
    provisioning = LinkPetWiFiProvisioning(args.device, args.pop)
    
    try:
        # 1. 扫描并连接
        await provisioning.scan_and_connect()
        
        # 2. 获取设备信息
        await provisioning.get_device_info()
        
        # 3. 启动配网会话
        if not await provisioning.start_provisioning_session():
            print("⚠️  会话启动失败，但继续尝试发送WiFi配置...")
        
        # 4. 发送WiFi凭据
        if not await provisioning.send_wifi_credentials(args.ssid, args.password):
            return False
        
        # 5. 检查连接状态
        success = await provisioning.check_connection_status(args.timeout)
        
        if success:
            print("\n🎉 LinkPet WiFi配网完成!")
        else:
            print("\n❌ WiFi配网失败")
            print("\n💡 建议:")
            print("  1. 使用官方ESP BLE Provisioning APP")
            print("  2. 检查WiFi密码是否正确")
            print("  3. 确保WiFi网络支持2.4GHz")
        
        return success
        
    except Exception as e:
        print(f"❌ 配网失败: {e}")
        return False
        
    finally:
        await provisioning.disconnect()

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断配网过程")
        exit(1)
