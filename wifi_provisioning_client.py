#!/usr/bin/env python3
"""
LinkPet WiFi配网客户端
通过BLE连接到LinkPet设备并进行WiFi配网

依赖安装:
pip install esp-idf-provisioning

使用方法:
python wifi_provisioning_client.py --ssid "你的WiFi名称" --passphrase "你的WiFi密码"
"""

import argparse
import time
import sys
from esp_prov import prov

def main():
    parser = argparse.ArgumentParser(description="LinkPet WiFi配网客户端")
    
    # WiFi配置参数
    parser.add_argument("--ssid", required=True, help="WiFi网络名称(SSID)")
    parser.add_argument("--passphrase", required=True, help="WiFi密码")
    
    # 设备配置参数
    parser.add_argument("--device-name", default="LinkPet-Device", 
                       help="BLE设备名称 (默认: LinkPet-Device)")
    parser.add_argument("--pop", default="abcd1234", 
                       help="所有权证明字符串 (默认: abcd1234)")
    parser.add_argument("--security", default="1", choices=["0", "1"],
                       help="安全级别: 0=无加密, 1=加密 (默认: 1)")
    
    # 其他选项
    parser.add_argument("--verbose", "-v", action="store_true", 
                       help="显示详细日志")
    parser.add_argument("--timeout", type=int, default=120,
                       help="配网超时时间(秒) (默认: 120)")
    
    args = parser.parse_args()
    
    print("=" * 50)
    print("LinkPet WiFi配网客户端")
    print("=" * 50)
    print(f"目标设备: {args.device_name}")
    print(f"WiFi SSID: {args.ssid}")
    print(f"安全级别: {args.security}")
    print("=" * 50)
    
    try:
        # 创建配网对象
        print("\n[1/6] 初始化BLE配网客户端...")
        
        # 配置传输层 - 使用BLE
        transport = prov.transport_ble(
            devname=args.device_name,
            service_uuid=None,  # 使用默认UUID
            nu_lookup=None
        )
        
        # 配置安全层
        if args.security == "1":
            print(f"[2/6] 配置安全连接 (POP: {args.pop})...")
            security = prov.security_1(pop=args.pop, verbose=args.verbose)
        else:
            print("[2/6] 配置无安全连接...")
            security = prov.security_0(verbose=args.verbose)
        
        # 创建配网会话
        print("[3/6] 建立配网会话...")
        session = prov.establish_session(transport, security)
        
        print("[4/6] 获取设备信息...")
        # 获取设备版本信息
        try:
            version_info = prov.version(session)
            print(f"    设备版本: {version_info}")
        except Exception as e:
            print(f"    警告: 无法获取版本信息 - {e}")
        
        # 扫描WiFi网络
        print("[5/6] 扫描WiFi网络...")
        try:
            wifi_scan_result = prov.scan_wifi_APs(session)
            print(f"    发现 {len(wifi_scan_result)} 个WiFi网络")
            
            # 检查目标SSID是否存在
            target_found = False
            for ap in wifi_scan_result:
                if ap['ssid'] == args.ssid:
                    target_found = True
                    print(f"    找到目标网络: {args.ssid} (信号强度: {ap['rssi']} dBm)")
                    break
            
            if not target_found:
                print(f"    警告: 未找到目标网络 '{args.ssid}'，但仍将尝试配网...")
                
        except Exception as e:
            print(f"    警告: WiFi扫描失败 - {e}")
        
        # 发送WiFi凭据
        print("[6/6] 发送WiFi配置...")
        prov.send_wifi_config(session, args.ssid, args.passphrase)
        print("    WiFi凭据已发送")
        
        # 等待配网完成
        print(f"\n等待配网完成 (超时: {args.timeout}秒)...")
        start_time = time.time()
        
        while True:
            elapsed = time.time() - start_time
            if elapsed > args.timeout:
                print("❌ 配网超时!")
                return False
                
            try:
                # 检查配网状态
                wifi_state = prov.get_wifi_config(session)
                print(f"    配网状态: {wifi_state}")
                
                if wifi_state['state'] == 'connected':
                    print("✅ WiFi配网成功!")
                    print(f"    已连接到: {wifi_state['ssid']}")
                    if 'ip' in wifi_state:
                        print(f"    IP地址: {wifi_state['ip']}")
                    break
                elif wifi_state['state'] == 'connection failed':
                    print("❌ WiFi连接失败!")
                    print("    请检查WiFi密码是否正确")
                    return False
                    
            except Exception as e:
                if args.verbose:
                    print(f"    状态检查异常: {e}")
            
            time.sleep(2)
        
        print("\n🎉 配网完成! LinkPet设备已成功连接到WiFi网络")
        return True
        
    except KeyboardInterrupt:
        print("\n\n⚠️  用户中断配网过程")
        return False
        
    except Exception as e:
        print(f"\n❌ 配网失败: {e}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        return False

def scan_devices():
    """扫描附近的LinkPet设备"""
    print("扫描附近的LinkPet设备...")
    try:
        # 这里可以添加BLE设备扫描逻辑
        # 由于esp-idf-provisioning库的限制，我们暂时跳过自动扫描
        print("请确保LinkPet设备已进入配网模式 (长按按键2)")
        print("设备名称通常为: LinkPet-Device")
    except Exception as e:
        print(f"设备扫描失败: {e}")

if __name__ == "__main__":
    print("LinkPet WiFi配网工具")
    print("请确保已安装依赖: pip install esp-idf-provisioning")
    print()
    
    # 检查是否提供了参数
    if len(sys.argv) == 1:
        print("使用示例:")
        print("python wifi_provisioning_client.py --ssid \"MyWiFi\" --passphrase \"MyPassword\"")
        print()
        print("可选参数:")
        print("  --device-name    BLE设备名称 (默认: LinkPet-Device)")
        print("  --pop           所有权证明字符串 (默认: abcd1234)")
        print("  --security      安全级别 0或1 (默认: 1)")
        print("  --verbose       显示详细日志")
        print("  --timeout       超时时间秒数 (默认: 120)")
        print()
        scan_devices()
        sys.exit(1)
    
    success = main()
    sys.exit(0 if success else 1)
