#!/usr/bin/env python3
"""
LinkPet BLE设备扫描和连接工具
兼容不同版本的bleak库

依赖安装:
pip install bleak

使用方法:
python linkpet_ble_scanner.py
"""

import asyncio
import sys
from bleak import BleakClient, BleakScanner

class LinkPetBLEScanner:
    def __init__(self):
        self.device_name = "LinkPet-Device"
        self.client = None
        
    async def scan_devices(self, timeout=15):
        """扫描LinkPet设备"""
        print(f"🔍 正在扫描 {self.device_name} 设备 (超时: {timeout}秒)...")
        print("请确保设备已进入配网模式 (长按按键2)")
        print()
        
        devices = await BleakScanner.discover(timeout=timeout)
        
        print(f"发现 {len(devices)} 个BLE设备:")
        linkpet_devices = []
        
        for i, device in enumerate(devices):
            device_name = device.name or "未知设备"
            print(f"  {i+1:2d}. {device_name} ({device.address})")
            
            if device.name and self.device_name in device.name:
                linkpet_devices.append(device)
                print(f"      ✅ 这是LinkPet设备!")
        
        print()
        return linkpet_devices
    
    async def connect_and_explore(self, device):
        """连接设备并探索服务"""
        print(f"🔗 连接到设备: {device.name} ({device.address})")
        
        try:
            async with BleakClient(device.address) as client:
                print("✅ 设备连接成功")
                
                # 等待服务发现完成
                await asyncio.sleep(2)
                
                print("\n📋 设备服务和特征:")
                print("-" * 60)
                
                # 使用正确的方式获取服务
                for service in client.services:
                    print(f"\n🔧 服务: {service.uuid}")
                    if service.description:
                        print(f"    描述: {service.description}")
                    
                    # 检查是否是配网相关服务
                    service_uuid_lower = service.uuid.lower()
                    if any(keyword in service_uuid_lower for keyword in ["ffff", "prov", "wifi"]):
                        print(f"    ⭐ 可能是配网服务!")
                    
                    # 列出特征
                    for char in service.characteristics:
                        props = []
                        for prop in char.properties:
                            if prop == "read":
                                props.append("读取")
                            elif prop == "write":
                                props.append("写入")
                            elif prop == "write-without-response":
                                props.append("写入(无响应)")
                            elif prop == "notify":
                                props.append("通知")
                            elif prop == "indicate":
                                props.append("指示")
                        
                        print(f"      📡 特征: {char.uuid}")
                        print(f"          属性: {', '.join(props)}")
                        
                        # 如果支持读取，尝试读取数据
                        if "read" in char.properties:
                            try:
                                data = await client.read_gatt_char(char.uuid)
                                if len(data) > 0:
                                    print(f"          数据: {data.hex()} ({len(data)} 字节)")
                                    # 尝试解码为文本
                                    try:
                                        text = data.decode('utf-8', errors='ignore')
                                        if text.isprintable():
                                            print(f"          文本: '{text}'")
                                    except:
                                        pass
                                else:
                                    print(f"          数据: 空")
                            except Exception as e:
                                print(f"          读取失败: {e}")
                
                print("\n" + "=" * 60)
                print("🎯 配网相关信息:")
                
                # 查找配网服务
                prov_services = []
                for service in client.services:
                    service_uuid = service.uuid.lower()
                    if any(keyword in service_uuid for keyword in ["ffff", "prov"]):
                        prov_services.append(service)
                
                if prov_services:
                    print(f"✅ 找到 {len(prov_services)} 个可能的配网服务:")
                    for service in prov_services:
                        print(f"  - {service.uuid}")
                        print(f"    特征数量: {len(service.characteristics)}")
                else:
                    print("❌ 未找到明显的配网服务")
                
                print("\n💡 下一步建议:")
                print("  1. 使用官方ESP BLE Provisioning APP进行配网")
                print("  2. 设备名称: LinkPet-Device")
                print("  3. POP密钥: abcd1234")
                print("  4. 安全级别: Security 1")
                
                return True
                
        except Exception as e:
            print(f"❌ 连接或探索失败: {e}")
            return False

async def main():
    print("=" * 60)
    print("LinkPet BLE设备扫描和探索工具")
    print("=" * 60)
    print("此工具用于扫描和探索LinkPet设备的BLE服务")
    print()
    
    scanner = LinkPetBLEScanner()
    
    try:
        # 扫描设备
        linkpet_devices = await scanner.scan_devices()
        
        if not linkpet_devices:
            print("❌ 未找到LinkPet设备")
            print("\n💡 请检查:")
            print("  1. 设备是否已进入配网模式 (长按按键2)")
            print("  2. 蓝牙是否已开启")
            print("  3. 设备是否在蓝牙范围内 (建议10米内)")
            print("  4. 设备是否已被其他程序连接")
            return False
        
        print(f"✅ 找到 {len(linkpet_devices)} 个LinkPet设备")
        
        # 连接第一个设备
        target_device = linkpet_devices[0]
        success = await scanner.connect_and_explore(target_device)
        
        return success
        
    except Exception as e:
        print(f"❌ 扫描过程出错: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_dependencies():
    """检查依赖"""
    try:
        import bleak
        print(f"✅ bleak 库已安装 (版本: {bleak.__version__})")
        return True
    except ImportError:
        print("❌ 缺少依赖: bleak")
        print("请运行: pip install bleak")
        return False
    except AttributeError:
        print("✅ bleak 库已安装")
        return True

if __name__ == "__main__":
    print("LinkPet BLE设备扫描工具")
    print()
    
    if not check_dependencies():
        sys.exit(1)
    
    try:
        success = asyncio.run(main())
        
        if success:
            print("\n🎉 设备探索完成!")
        else:
            print("\n❌ 设备探索失败")
            
        input("\n按回车键退出...")
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断扫描过程")
        sys.exit(1)
