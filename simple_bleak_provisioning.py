#!/usr/bin/env python3
"""
简化的LinkPet WiFi配网客户端
使用Bleak库与ESP-IDF标准配网协议通信

依赖安装:
pip install bleak protobuf

使用方法:
python simple_bleak_provisioning.py
"""

import asyncio
import sys
from bleak import BleakClient, BleakScanner

# ESP-IDF WiFi配网服务UUID
PROV_SERVICE_UUID = "0000ffff-0000-1000-8000-00805f9b34fb"

class SimpleProvisioning:
    def __init__(self):
        self.client = None
        self.device_name = "LinkPet-Device"
        
    async def scan_for_device(self, timeout=10):
        """扫描LinkPet设备"""
        print(f"正在扫描 {self.device_name} 设备...")
        
        devices = await BleakScanner.discover(timeout=timeout)
        
        for device in devices:
            if device.name and self.device_name in device.name:
                print(f"✅ 找到设备: {device.name} ({device.address})")
                return device
        
        return None
    
    async def connect_device(self, device):
        """连接到设备"""
        print(f"连接到设备: {device.address}")
        
        self.client = BleakClient(device.address)
        await self.client.connect()
        
        if self.client.is_connected:
            print("✅ 设备连接成功")
            
            # 列出所有服务和特征
            try:
                services = self.client.services
                print("\n📋 设备服务列表:")

                for service in services:
                    print(f"  服务: {service.uuid}")
                    for char in service.characteristics:
                        props = []
                        if "read" in char.properties:
                            props.append("读")
                        if "write" in char.properties:
                            props.append("写")
                        if "notify" in char.properties:
                            props.append("通知")

                        print(f"    特征: {char.uuid} ({', '.join(props)})")
            except Exception as e:
                print(f"⚠️  获取服务列表失败: {e}")
                print("尝试使用备用方法...")
            
            return True
        else:
            print("❌ 设备连接失败")
            return False
    
    async def find_provisioning_service(self):
        """查找配网服务"""
        try:
            services = self.client.services
        except AttributeError:
            print("⚠️  无法直接访问services属性，尝试其他方法")
            return None

        for service in services:
            # 检查是否是配网服务
            if PROV_SERVICE_UUID.lower() in service.uuid.lower():
                print(f"✅ 找到配网服务: {service.uuid}")
                return service

            # 也检查其他可能的配网服务UUID
            if "ffff" in service.uuid.lower():
                print(f"🔍 可能的配网服务: {service.uuid}")
                return service

        print("❌ 未找到配网服务")
        return None
    
    async def list_characteristics(self, service):
        """列出服务的所有特征"""
        print(f"\n📋 服务 {service.uuid} 的特征:")
        
        characteristics = []
        for char in service.characteristics:
            props = []
            if "read" in char.properties:
                props.append("读")
            if "write" in char.properties:
                props.append("写")
            if "write-without-response" in char.properties:
                props.append("写(无响应)")
            if "notify" in char.properties:
                props.append("通知")
            
            print(f"  {char.uuid}: {', '.join(props)}")
            characteristics.append(char)
        
        return characteristics
    
    async def test_characteristic_communication(self, char):
        """测试特征通信"""
        print(f"\n🧪 测试特征: {char.uuid}")
        
        try:
            # 如果支持读取，尝试读取
            if "read" in char.properties:
                data = await self.client.read_gatt_char(char)
                print(f"  读取数据: {data.hex()} ({len(data)} 字节)")
                
                # 尝试解码为文本
                try:
                    text = data.decode('utf-8')
                    print(f"  文本内容: {text}")
                except:
                    pass
            
            # 如果支持写入，发送测试数据
            if "write" in char.properties or "write-without-response" in char.properties:
                test_data = b"hello"
                await self.client.write_gatt_char(char, test_data)
                print(f"  发送测试数据: {test_data}")
                
        except Exception as e:
            print(f"  ❌ 通信失败: {e}")
    
    async def disconnect(self):
        """断开连接"""
        if self.client and self.client.is_connected:
            await self.client.disconnect()
            print("🔌 设备连接已断开")

async def main():
    print("=" * 50)
    print("LinkPet WiFi配网设备探测工具")
    print("=" * 50)
    print("此工具用于探测LinkPet设备的BLE服务和特征")
    print("请确保设备已进入配网模式 (长按按键2)")
    print("=" * 50)
    
    provisioning = SimpleProvisioning()
    
    try:
        # 1. 扫描设备
        device = await provisioning.scan_for_device(timeout=15)
        if not device:
            print("❌ 未找到LinkPet设备")
            print("\n💡 请检查:")
            print("  1. 设备是否已进入配网模式 (长按按键2)")
            print("  2. 蓝牙是否已开启")
            print("  3. 设备是否在蓝牙范围内")
            return False
        
        # 2. 连接设备
        if not await provisioning.connect_device(device):
            return False
        
        # 3. 查找配网服务
        prov_service = await provisioning.find_provisioning_service()
        if not prov_service:
            print("\n💡 这可能意味着:")
            print("  1. 设备未进入配网模式")
            print("  2. 配网服务使用了不同的UUID")
            print("  3. 需要特殊的连接序列")
            return False
        
        # 4. 列出特征
        characteristics = await provisioning.list_characteristics(prov_service)
        
        # 5. 测试特征通信
        print(f"\n🧪 测试特征通信...")
        for char in characteristics[:3]:  # 只测试前3个特征
            await provisioning.test_characteristic_communication(char)
        
        print("\n✅ 设备探测完成!")
        print("\n📝 下一步:")
        print("  1. 记录上述服务和特征UUID")
        print("  2. 分析设备的配网协议")
        print("  3. 实现具体的配网逻辑")
        
        return True
        
    except Exception as e:
        print(f"❌ 探测过程出错: {e}")
        return False
        
    finally:
        await provisioning.disconnect()

def check_dependencies():
    """检查依赖"""
    try:
        import bleak
        print("✅ bleak 库已安装")
        return True
    except ImportError:
        print("❌ 缺少依赖: bleak")
        print("请运行: pip install bleak")
        return False

if __name__ == "__main__":
    print("LinkPet BLE设备探测工具")
    print()
    
    if not check_dependencies():
        sys.exit(1)
    
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️  用户中断探测过程")
        sys.exit(1)
